#!/usr/bin/env python3
"""
Test script to verify that chat suggestions have been successfully removed
"""

import sys
import os

# Add the api directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'api'))

def test_imports():
    """Test that imports work correctly after removing suggestions"""
    try:
        from gemini_service import get_medical_analysis, chat_with_medical_assistant, detect_mri_scan
        print("✅ Successfully imported functions from gemini_service (without get_chat_suggestions)")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_function_removal():
    """Test that get_chat_suggestions function has been removed"""
    try:
        import gemini_service
        if hasattr(gemini_service, 'get_chat_suggestions'):
            print("❌ get_chat_suggestions function still exists in gemini_service")
            return False
        else:
            print("✅ get_chat_suggestions function successfully removed from gemini_service")
            return True
    except Exception as e:
        print(f"❌ Error checking function removal: {e}")
        return False

def test_app_imports():
    """Test that app.py imports work correctly"""
    try:
        # This will test if the import statement in app.py is correct
        import app
        print("✅ app.py imports successfully")
        return True
    except ImportError as e:
        print(f"❌ app.py import error: {e}")
        return False

def main():
    print("Testing chat suggestions removal...")
    print("=" * 50)
    
    tests = [
        ("Import test", test_imports),
        ("Function removal test", test_function_removal),
        ("App import test", test_app_imports)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Chat suggestions have been successfully removed.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
